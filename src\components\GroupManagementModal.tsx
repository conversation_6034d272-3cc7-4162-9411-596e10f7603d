import React, { useState, useEffect, useCallback } from 'react';
import { Modal, Table, Input, Button, Space, message, Popconfirm } from 'antd';
import { SearchOutlined, ReloadOutlined, EditOutlined, DeleteOutlined, PlusOutlined, TeamOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { TaskBasicGroup } from '../types/task';

interface GroupManagementModalProps {
  visible: boolean;
  onCancel: () => void;
}

const GroupManagementModal: React.FC<GroupManagementModalProps> = ({
  visible,
  onCancel,
}) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<TaskBasicGroup[]>([]);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 生成1000条模拟数据
  const generateMockData = (): TaskBasicGroup[] => {
    const groupNames = [
      '数据同步', '报表生成', '数据清理', '系统监控', '备份任务',
      '日志分析', '性能优化', '安全检查', '数据迁移', '接口测试',
      '用户管理', '权限控制', '消息推送', '文件处理', '图片压缩',
      '视频转码', '邮件发送', '短信通知', '支付处理', '订单同步',
      '库存管理', '价格更新', '促销活动', '客户服务', '数据统计',
      '业务分析', '风险控制', '合规检查', '审计日志', '系统升级',
      '配置管理', '环境部署', '代码发布', '版本控制', '质量检测',
      '自动化测试', '压力测试', '兼容性测试', '安全扫描', '漏洞修复',
      '数据备份', '灾难恢复', '容量规划', '资源监控', '告警处理',
      '故障排查', '性能调优', '缓存清理', '索引优化', '查询优化'
    ];

    const data: TaskBasicGroup[] = [];

    for (let i = 1; i <= 1000; i++) {
      const baseNameIndex = (i - 1) % groupNames.length;
      const suffix = Math.floor((i - 1) / groupNames.length) + 1;
      const name = suffix === 1 ? groupNames[baseNameIndex] : `${groupNames[baseNameIndex]}_${suffix}`;

      // 生成随机日期（最近一年内）
      const createDate = new Date();
      createDate.setDate(createDate.getDate() - Math.floor(Math.random() * 365));
      const createTime = createDate.toISOString().slice(0, 19).replace('T', ' ');

      // 更新时间在创建时间之后
      const updateDate = new Date(createDate);
      updateDate.setDate(updateDate.getDate() + Math.floor(Math.random() * 30));
      const updateTime = updateDate.toISOString().slice(0, 19).replace('T', ' ');

      data.push({
        id: i,
        name,
        create_time: createTime,
        update_time: updateTime,
      });
    }

    return data;
  };

  const mockData: TaskBasicGroup[] = generateMockData();

  // 加载数据
  const loadData = useCallback(async (params?: { name?: string; current?: number; pageSize?: number }) => {
    setLoading(true);
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 500));
      
      let filteredData = mockData;
      
      // 按名称搜索
      if (params?.name) {
        filteredData = mockData.filter(item => 
          item.name.toLowerCase().includes(params.name!.toLowerCase())
        );
      }
      
      const current = params?.current || pagination.current;
      const pageSize = params?.pageSize || pagination.pageSize;
      const start = (current - 1) * pageSize;
      const end = start + pageSize;
      
      const pageData = filteredData.slice(start, end);
      
      setData(pageData);
      setPagination(prev => ({
        ...prev,
        current,
        pageSize,
        total: filteredData.length,
      }));
      
      message.success('数据加载成功');
    } catch (error) {
      message.error('数据加载失败'+error);
    } finally {
      setLoading(false);
    }
  }, [mockData, pagination]);

  // 搜索
  const handleSearch = () => {
    loadData({
      name: searchText,
      current: 1,
      pageSize: pagination.pageSize,
    });
  };

  // 重置搜索
  const handleReset = () => {
    setSearchText('');
    loadData({
      current: 1,
      pageSize: pagination.pageSize,
    });
  };

  // 编辑分组
  const handleEdit = (record: TaskBasicGroup) => {
    message.info(`编辑分组: ${record.name}`);
    // TODO: 实现编辑功能
  };

  // 删除分组
  const handleDelete = async (record: TaskBasicGroup) => {
    try {
      // 模拟删除API请求
      await new Promise(resolve => setTimeout(resolve, 300));
      message.success(`删除分组 "${record.name}" 成功`);
      loadData();
    } catch (error) {
      message.error('删除失败'+error);
    }
  };

  // 新增分组
  const handleAdd = () => {
    message.info('新增分组');
    // TODO: 实现新增功能
  };

  // 表格列定义
  const columns: ColumnsType<TaskBasicGroup> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '分组名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 180,
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      key: 'update_time',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description={`确定要删除分组 "${record.name}" 吗？`}
            onConfirm={() => handleDelete(record)}
            okText="确认"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 分页变化处理
  const handleTableChange = (page: number, pageSize: number) => {
    loadData({
      name: searchText,
      current: page,
      pageSize,
    });
  };

  // 初始化数据
  useEffect(() => {
    if (visible) {
      loadData();
    }
  }, [visible, loadData]);

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <TeamOutlined className="text-blue-500" />
          <span>分组管理</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={1000}
      footer={null}
      destroyOnClose={true}
      styles={{
        body: { padding: '20px 24px' }
      }}
    >
      {/* 搜索区域 */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <Input
              placeholder="请输入分组名称"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              onPressEnter={handleSearch}
              style={{ width: 280 }}
              allowClear
              className="shadow-sm"
            />
          </div>
          <div>
            <Space>
              <Button type="primary" onClick={handleSearch} className="shadow-sm">
                搜索
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset} className="shadow-sm">
                重置
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
                className="shadow-sm"
                style={{
                  background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                  border: 'none'
                }}
              >
                新增分组
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 表格 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <Table
          columns={columns}
          dataSource={data}
          loading={loading}
          rowKey="id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          size="small"
          className="rounded-lg overflow-hidden"
          scroll={{ y: 400 }}
        />
      </div>
    </Modal>
  );
};

export default GroupManagementModal;
